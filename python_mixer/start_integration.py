#!/usr/bin/env python3
"""
🚀 STABILNY SZKIELET INTEGRACJI
Główny skrypt uruchamiający spójną integrację Python Mixer ↔ GoBackend-Kratos
"""

import asyncio
import logging
import signal
import sys
from pathlib import Path
import json
from datetime import datetime

# Dodaj ścieżkę do modułów
sys.path.append(str(Path(__file__).parent))

from core.integration.orchestrator import integration_orchestrator
from core.integration.event_system import EventType, EventPriority

# Konfiguracja logowania
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('integration.log'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

class IntegrationStarter:
    """🚀 Starter integracji"""
    
    def __init__(self):
        self.orchestrator = integration_orchestrator
        self.shutdown_event = asyncio.Event()
        
    async def start(self):
        """🚀 Uruchom pełną integrację"""
        logger.info("🚀 Uruchamianie STABILNEGO SZKIELETU INTEGRACJI")
        logger.info("=" * 60)
        
        try:
            # 1. Inicjalizuj orkiestrator
            logger.info("🎼 Inicjalizacja Integration Orchestrator...")
            success = await self.orchestrator.initialize()
            
            if not success:
                logger.error("❌ Nie udało się zainicjalizować orkiestratora")
                return False
            
            # 2. Przetwórz istniejące archiwum emaili dolores
            logger.info("📧 Przetwarzanie archiwum emaili dolores...")
            await self.orchestrator.process_dolores_email_archive()
            
            # 3. Uruchom pełną synchronizację
            logger.info("🔄 Uruchamianie pełnej synchronizacji...")
            sync_result = await self.orchestrator.sync_manager.perform_full_sync()
            
            if sync_result.get("success"):
                logger.info(f"✅ Pełna synchronizacja ukończona: {sync_result}")
            else:
                logger.warning(f"⚠️ Problemy z synchronizacją: {sync_result}")
            
            # 4. Wyświetl status integracji
            await self._display_integration_status()
            
            # 5. Uruchom monitoring w czasie rzeczywistym
            logger.info("📊 Uruchamianie monitoringu w czasie rzeczywistym...")
            await self._start_real_time_monitoring()
            
            logger.info("✅ STABILNY SZKIELET INTEGRACJI URUCHOMIONY POMYŚLNIE!")
            logger.info("=" * 60)
            
            # Czekaj na sygnał zamknięcia
            await self.shutdown_event.wait()
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Błąd uruchamiania integracji: {e}")
            return False
        
        finally:
            await self._shutdown()
    
    async def _display_integration_status(self):
        """📊 Wyświetl status integracji"""
        status = await self.orchestrator.get_status()
        health = status["health"]
        
        logger.info("📊 STATUS INTEGRACJI:")
        logger.info(f"   🏥 Ogólne zdrowie: {health['overall_score']:.1f}%")
        logger.info(f"   ⏱️  Czas działania: {status['uptime']:.0f}s")
        logger.info(f"   📧 Przetworzone emaile: {status['metrics']['emails_processed']}")
        logger.info(f"   📝 Ukończone transkrypcje: {status['metrics']['transcriptions_completed']}")
        logger.info(f"   🔄 Operacje synchronizacji: {status['metrics']['sync_operations']}")
        logger.info(f"   ⚡ Wyemitowane zdarzenia: {status['metrics']['events_emitted']}")
        
        # Wyświetl status komponentów
        logger.info("🔧 KOMPONENTY:")
        for component, data in health["components"].items():
            if isinstance(data, dict) and "health_score" in data:
                score = data["health_score"]
                logger.info(f"   {component}: {score:.1f}%")
            elif isinstance(data, dict) and "success_rate" in data:
                rate = data["success_rate"]
                logger.info(f"   {component}: {rate:.1f}% sukcesu")
            else:
                logger.info(f"   {component}: OK")
        
        # Wyświetl problemy jeśli są
        if health["issues"]:
            logger.warning("⚠️  PROBLEMY:")
            for issue in health["issues"]:
                logger.warning(f"   - {issue}")
        
        # Wyświetl rekomendacje
        if health["recommendations"]:
            logger.info("💡 REKOMENDACJE:")
            for rec in health["recommendations"]:
                logger.info(f"   - {rec}")
    
    async def _start_real_time_monitoring(self):
        """📊 Uruchom monitoring w czasie rzeczywistym"""
        
        # Subskrybuj kluczowe zdarzenia
        def log_important_events(event):
            if event.type in [
                EventType.TRANSCRIPTION_COMPLETED,
                EventType.SYNC_COMPLETED,
                EventType.SERVICE_OFFLINE,
                EventType.SERVICE_DEGRADED
            ]:
                logger.info(f"⚡ Zdarzenie: {event.type.value} z {event.source}")
                if event.data:
                    logger.info(f"   Dane: {event.data}")
        
        self.orchestrator.event_system.subscribe(
            [
                EventType.TRANSCRIPTION_COMPLETED,
                EventType.TRANSCRIPTION_FAILED,
                EventType.SYNC_COMPLETED,
                EventType.SYNC_FAILED,
                EventType.SERVICE_ONLINE,
                EventType.SERVICE_OFFLINE,
                EventType.SERVICE_DEGRADED
            ],
            log_important_events
        )
        
        # Uruchom okresowe raporty
        asyncio.create_task(self._periodic_status_reports())
    
    async def _periodic_status_reports(self):
        """📈 Okresowe raporty statusu"""
        while not self.shutdown_event.is_set():
            try:
                await asyncio.sleep(300)  # Co 5 minut
                
                logger.info("📈 RAPORT OKRESOWY:")
                await self._display_integration_status()
                
                # Zapisz szczegółowy raport
                await self._save_detailed_report()
                
            except Exception as e:
                logger.error(f"❌ Błąd w okresowym raporcie: {e}")
                await asyncio.sleep(60)
    
    async def _save_detailed_report(self):
        """💾 Zapisz szczegółowy raport"""
        try:
            status = await self.orchestrator.get_status()
            
            report = {
                "timestamp": datetime.now().isoformat(),
                "integration_status": status,
                "system_info": {
                    "python_version": sys.version,
                    "platform": sys.platform
                }
            }
            
            report_path = Path("integration_report.json")
            with open(report_path, 'w') as f:
                json.dump(report, f, indent=2, default=str)
            
            logger.debug(f"💾 Raport zapisany: {report_path}")
            
        except Exception as e:
            logger.error(f"❌ Błąd zapisywania raportu: {e}")
    
    def _setup_signal_handlers(self):
        """⚡ Ustaw handlery sygnałów"""
        def signal_handler(signum, frame):
            logger.info(f"⚡ Otrzymano sygnał {signum}, zamykanie...")
            self.shutdown_event.set()
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    async def _shutdown(self):
        """🛑 Zamknij integrację"""
        logger.info("🛑 Zamykanie integracji...")
        
        try:
            await self.orchestrator.shutdown()
            logger.info("✅ Integracja zamknięta pomyślnie")
            
        except Exception as e:
            logger.error(f"❌ Błąd zamykania integracji: {e}")

async def main():
    """🎯 Główna funkcja"""
    starter = IntegrationStarter()
    starter._setup_signal_handlers()
    
    try:
        success = await starter.start()
        
        if success:
            logger.info("🎉 INTEGRACJA ZAKOŃCZONA POMYŚLNIE")
            return 0
        else:
            logger.error("💥 INTEGRACJA ZAKOŃCZONA Z BŁĘDAMI")
            return 1
            
    except KeyboardInterrupt:
        logger.info("⌨️  Przerwano przez użytkownika")
        return 0
    except Exception as e:
        logger.error(f"💥 Nieoczekiwany błąd: {e}")
        return 1

def check_dependencies():
    """🔍 Sprawdź zależności"""
    required_modules = [
        'aiohttp', 'aiofiles', 'asyncio', 'websockets', 
        'aiosqlite', 'backoff'
    ]
    
    missing = []
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing.append(module)
    
    if missing:
        logger.error(f"❌ Brakujące moduły: {missing}")
        logger.info("💡 Zainstaluj: pip install " + " ".join(missing))
        return False
    
    return True

def display_banner():
    """🎨 Wyświetl banner"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║        🌉 STABILNY SZKIELET INTEGRACJI 🌉                    ║
    ║                                                              ║
    ║     Python Mixer ↔ GoBackend-Kratos Integration             ║
    ║                                                              ║
    ║  🎼 Orchestrator  🔄 Sync Manager  ⚡ Event System          ║
    ║  🌉 Bridge       🔗 API Client    📊 Monitoring             ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

if __name__ == "__main__":
    display_banner()
    
    # Sprawdź zależności
    if not check_dependencies():
        sys.exit(1)
    
    # Uruchom integrację
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
