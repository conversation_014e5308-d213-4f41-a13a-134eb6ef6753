#!/usr/bin/env python3
"""
🌉 UNIFIED INTEGRATION BRIDGE - STABILNY SZKIELET
Centralny most łączący Python Mixer z GoBackend-Kratos
Zapewnia spójną komunikację i synchronizację danych
"""

import asyncio
import aiohttp
import json
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from pathlib import Path
import websockets
from enum import Enum

logger = logging.getLogger(__name__)

class ServiceStatus(Enum):
    """Status usług w ekosystemie"""
    ONLINE = "online"
    OFFLINE = "offline"
    DEGRADED = "degraded"
    MAINTENANCE = "maintenance"

@dataclass
class ServiceEndpoint:
    """Definicja endpointu usługi"""
    name: str
    url: str
    port: int
    health_path: str = "/health"
    status: ServiceStatus = ServiceStatus.OFFLINE
    last_check: Optional[datetime] = None

@dataclass
class TranscriptionData:
    """Dane transkrypcji do przekazania"""
    email_id: str
    file_path: str
    transcript: str
    confidence: float
    language: str
    processing_time: float
    metadata: Dict[str, Any]
    timestamp: datetime

@dataclass
class EmailMetadata:
    """Metadane emaila"""
    email_id: str
    subject: str
    sender: str
    recipient: str
    date: datetime
    has_attachments: bool
    attachment_count: int
    m4a_files: List[str]

class UnifiedIntegrationBridge:
    """🌉 Centralny most integracji"""
    
    def __init__(self):
        # Konfiguracja usług
        self.services = {
            "gobackend_crm": ServiceEndpoint(
                name="GoBackend HVAC CRM",
                url="http://localhost",
                port=8080,
                health_path="/api/health"
            ),
            "gobackend_mcp": ServiceEndpoint(
                name="GoBackend MCP Server",
                url="http://localhost",
                port=8081,
                health_path="/health"
            ),
            "email_intelligence": ServiceEndpoint(
                name="Email Intelligence",
                url="http://localhost",
                port=8082,
                health_path="/health"
            ),
            "nvidia_stt": ServiceEndpoint(
                name="NVIDIA STT Service",
                url="http://localhost",
                port=8889,
                health_path="/health"
            ),
            "orchestrator": ServiceEndpoint(
                name="Python Mixer Orchestrator",
                url="http://localhost",
                port=9000,
                health_path="/health"
            )
        }
        
        # Kolejki danych
        self.transcription_queue = asyncio.Queue()
        self.email_metadata_queue = asyncio.Queue()
        self.sync_queue = asyncio.Queue()
        
        # Status integracji
        self.integration_status = {
            "active_connections": 0,
            "total_synced": 0,
            "last_sync": None,
            "errors": 0
        }
        
        # WebSocket connections
        self.ws_connections = {}
        
    async def initialize(self) -> bool:
        """🚀 Inicjalizacja mostu integracji"""
        logger.info("🌉 Inicjalizacja Unified Integration Bridge...")
        
        try:
            # Sprawdź dostępność usług
            await self.check_all_services()
            
            # Uruchom monitoring
            asyncio.create_task(self.service_monitor())
            
            # Uruchom procesory kolejek
            asyncio.create_task(self.process_transcription_queue())
            asyncio.create_task(self.process_email_metadata_queue())
            asyncio.create_task(self.process_sync_queue())
            
            # Uruchom WebSocket server
            asyncio.create_task(self.start_websocket_server())
            
            logger.info("✅ Unified Integration Bridge zainicjalizowany pomyślnie")
            return True
            
        except Exception as e:
            logger.error(f"❌ Błąd inicjalizacji Integration Bridge: {e}")
            return False
    
    async def check_all_services(self):
        """🔍 Sprawdź status wszystkich usług"""
        logger.info("🔍 Sprawdzanie statusu wszystkich usług...")
        
        for service_name, service in self.services.items():
            await self.check_service_health(service_name)
    
    async def check_service_health(self, service_name: str) -> bool:
        """🏥 Sprawdź zdrowie konkretnej usługi"""
        service = self.services.get(service_name)
        if not service:
            return False
        
        try:
            url = f"{service.url}:{service.port}{service.health_path}"
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, timeout=aiohttp.ClientTimeout(total=5)) as response:
                    if response.status == 200:
                        service.status = ServiceStatus.ONLINE
                        service.last_check = datetime.now()
                        logger.info(f"✅ {service.name} - ONLINE")
                        return True
                    else:
                        service.status = ServiceStatus.DEGRADED
                        logger.warning(f"⚠️ {service.name} - DEGRADED (status: {response.status})")
                        return False
                        
        except Exception as e:
            service.status = ServiceStatus.OFFLINE
            service.last_check = datetime.now()
            logger.error(f"❌ {service.name} - OFFLINE: {e}")
            return False
    
    async def service_monitor(self):
        """📊 Ciągły monitoring usług"""
        while True:
            try:
                await self.check_all_services()
                await self.update_integration_status()
                await asyncio.sleep(30)  # Sprawdzaj co 30 sekund
                
            except Exception as e:
                logger.error(f"❌ Błąd w monitoringu usług: {e}")
                await asyncio.sleep(60)
    
    async def update_integration_status(self):
        """📈 Aktualizuj status integracji"""
        online_services = sum(1 for s in self.services.values() if s.status == ServiceStatus.ONLINE)
        self.integration_status["active_connections"] = online_services
        
        # Zapisz status do pliku
        status_file = Path("integration_status.json")
        with open(status_file, 'w') as f:
            json.dump({
                "services": {name: asdict(service) for name, service in self.services.items()},
                "integration_status": self.integration_status,
                "timestamp": datetime.now().isoformat()
            }, f, indent=2, default=str)
    
    async def send_transcription_to_gobackend(self, transcription: TranscriptionData) -> bool:
        """📤 Wyślij dane transkrypcji do GoBackend"""
        try:
            service = self.services["gobackend_crm"]
            if service.status != ServiceStatus.ONLINE:
                logger.warning(f"⚠️ GoBackend CRM nie jest dostępny - dodaję do kolejki")
                await self.transcription_queue.put(transcription)
                return False
            
            url = f"{service.url}:{service.port}/api/v1/transcriptions"
            data = asdict(transcription)
            
            async with aiohttp.ClientSession() as session:
                async with session.post(url, json=data) as response:
                    if response.status == 200:
                        logger.info(f"✅ Transkrypcja {transcription.email_id} wysłana do GoBackend")
                        self.integration_status["total_synced"] += 1
                        self.integration_status["last_sync"] = datetime.now()
                        return True
                    else:
                        logger.error(f"❌ Błąd wysyłania transkrypcji: {response.status}")
                        await self.transcription_queue.put(transcription)
                        return False
                        
        except Exception as e:
            logger.error(f"❌ Błąd wysyłania transkrypcji do GoBackend: {e}")
            await self.transcription_queue.put(transcription)
            self.integration_status["errors"] += 1
            return False
    
    async def send_email_metadata_to_gobackend(self, metadata: EmailMetadata) -> bool:
        """📧 Wyślij metadane emaila do GoBackend"""
        try:
            service = self.services["gobackend_crm"]
            if service.status != ServiceStatus.ONLINE:
                await self.email_metadata_queue.put(metadata)
                return False
            
            url = f"{service.url}:{service.port}/api/v1/emails/metadata"
            data = asdict(metadata)
            
            async with aiohttp.ClientSession() as session:
                async with session.post(url, json=data) as response:
                    if response.status == 200:
                        logger.info(f"✅ Metadane emaila {metadata.email_id} wysłane do GoBackend")
                        return True
                    else:
                        logger.error(f"❌ Błąd wysyłania metadanych: {response.status}")
                        return False
                        
        except Exception as e:
            logger.error(f"❌ Błąd wysyłania metadanych do GoBackend: {e}")
            await self.email_metadata_queue.put(metadata)
            return False
    
    async def process_transcription_queue(self):
        """🔄 Przetwarzaj kolejkę transkrypcji"""
        while True:
            try:
                transcription = await self.transcription_queue.get()
                success = await self.send_transcription_to_gobackend(transcription)
                
                if not success:
                    # Ponów próbę za 60 sekund
                    await asyncio.sleep(60)
                    await self.transcription_queue.put(transcription)
                    
            except Exception as e:
                logger.error(f"❌ Błąd przetwarzania kolejki transkrypcji: {e}")
                await asyncio.sleep(30)
    
    async def process_email_metadata_queue(self):
        """📧 Przetwarzaj kolejkę metadanych emaili"""
        while True:
            try:
                metadata = await self.email_metadata_queue.get()
                await self.send_email_metadata_to_gobackend(metadata)
                
            except Exception as e:
                logger.error(f"❌ Błąd przetwarzania kolejki metadanych: {e}")
                await asyncio.sleep(30)
    
    async def process_sync_queue(self):
        """🔄 Przetwarzaj kolejkę synchronizacji"""
        while True:
            try:
                sync_data = await self.sync_queue.get()
                await self.handle_sync_request(sync_data)
                
            except Exception as e:
                logger.error(f"❌ Błąd przetwarzania kolejki sync: {e}")
                await asyncio.sleep(30)
    
    async def handle_sync_request(self, sync_data: Dict[str, Any]):
        """🔄 Obsłuż żądanie synchronizacji"""
        sync_type = sync_data.get("type")
        
        if sync_type == "full_sync":
            await self.perform_full_sync()
        elif sync_type == "incremental_sync":
            await self.perform_incremental_sync(sync_data.get("since"))
        else:
            logger.warning(f"⚠️ Nieznany typ synchronizacji: {sync_type}")
    
    async def perform_full_sync(self):
        """🔄 Wykonaj pełną synchronizację"""
        logger.info("🔄 Rozpoczynanie pełnej synchronizacji...")
        
        # Synchronizuj wszystkie dane z dolores_email_archive
        archive_path = Path("dolores_email_archive")
        
        if archive_path.exists():
            # Synchronizuj metadane
            metadata_files = list((archive_path / "metadata").glob("*.json"))
            for metadata_file in metadata_files:
                try:
                    with open(metadata_file, 'r') as f:
                        data = json.load(f)
                    
                    metadata = EmailMetadata(
                        email_id=data["email_id"],
                        subject=data["subject"],
                        sender=data["from"],
                        recipient=data["to"],
                        date=datetime.fromisoformat(data["processed_timestamp"]),
                        has_attachments=data["has_attachments"],
                        attachment_count=data["attachment_count"],
                        m4a_files=data["m4a_attachments"]
                    )
                    
                    await self.send_email_metadata_to_gobackend(metadata)
                    
                except Exception as e:
                    logger.error(f"❌ Błąd synchronizacji metadanych {metadata_file}: {e}")
            
            # Synchronizuj transkrypcje
            transcription_files = list((archive_path / "transcriptions").glob("transcription_*.json"))
            for transcription_file in transcription_files:
                try:
                    with open(transcription_file, 'r') as f:
                        data = json.load(f)
                    
                    transcription = TranscriptionData(
                        email_id=data["file_info"]["email_id"],
                        file_path=data["file_info"]["original_file"],
                        transcript=data["text"],
                        confidence=data["confidence"],
                        language=data["language"],
                        processing_time=data["processing_time"],
                        metadata=data["file_info"],
                        timestamp=datetime.now()
                    )
                    
                    await self.send_transcription_to_gobackend(transcription)
                    
                except Exception as e:
                    logger.error(f"❌ Błąd synchronizacji transkrypcji {transcription_file}: {e}")
        
        logger.info("✅ Pełna synchronizacja zakończona")
    
    async def perform_incremental_sync(self, since: datetime):
        """📈 Wykonaj synchronizację przyrostową"""
        logger.info(f"📈 Synchronizacja przyrostowa od {since}")
        # Implementacja synchronizacji przyrostowej
        pass
    
    async def start_websocket_server(self):
        """🔌 Uruchom serwer WebSocket dla real-time komunikacji"""
        async def handle_websocket(websocket, path):
            try:
                self.ws_connections[id(websocket)] = websocket
                logger.info(f"🔌 Nowe połączenie WebSocket: {path}")
                
                async for message in websocket:
                    data = json.loads(message)
                    await self.handle_websocket_message(websocket, data)
                    
            except Exception as e:
                logger.error(f"❌ Błąd WebSocket: {e}")
            finally:
                if id(websocket) in self.ws_connections:
                    del self.ws_connections[id(websocket)]
        
        try:
            await websockets.serve(handle_websocket, "localhost", 9001)
            logger.info("🔌 Serwer WebSocket uruchomiony na porcie 9001")
        except Exception as e:
            logger.error(f"❌ Błąd uruchamiania serwera WebSocket: {e}")
    
    async def handle_websocket_message(self, websocket, data: Dict[str, Any]):
        """📨 Obsłuż wiadomość WebSocket"""
        message_type = data.get("type")
        
        if message_type == "status_request":
            await self.send_status_update(websocket)
        elif message_type == "sync_request":
            await self.sync_queue.put(data)
        else:
            logger.warning(f"⚠️ Nieznany typ wiadomości WebSocket: {message_type}")
    
    async def send_status_update(self, websocket):
        """📊 Wyślij aktualizację statusu przez WebSocket"""
        status_data = {
            "type": "status_update",
            "services": {name: asdict(service) for name, service in self.services.items()},
            "integration_status": self.integration_status,
            "timestamp": datetime.now().isoformat()
        }
        
        await websocket.send(json.dumps(status_data, default=str))
    
    async def broadcast_status_update(self):
        """📡 Rozgłoś aktualizację statusu do wszystkich połączeń"""
        if self.ws_connections:
            status_data = {
                "type": "status_update",
                "services": {name: asdict(service) for name, service in self.services.items()},
                "integration_status": self.integration_status,
                "timestamp": datetime.now().isoformat()
            }
            
            message = json.dumps(status_data, default=str)
            
            for websocket in list(self.ws_connections.values()):
                try:
                    await websocket.send(message)
                except Exception as e:
                    logger.error(f"❌ Błąd wysyłania broadcast: {e}")
    
    def get_integration_health(self) -> Dict[str, Any]:
        """🏥 Pobierz stan zdrowia integracji"""
        online_services = sum(1 for s in self.services.values() if s.status == ServiceStatus.ONLINE)
        total_services = len(self.services)
        
        health_score = (online_services / total_services) * 100
        
        return {
            "health_score": health_score,
            "online_services": online_services,
            "total_services": total_services,
            "services": {name: service.status.value for name, service in self.services.items()},
            "integration_status": self.integration_status,
            "last_update": datetime.now().isoformat()
        }

# Globalna instancja mostu integracji
integration_bridge = UnifiedIntegrationBridge()
