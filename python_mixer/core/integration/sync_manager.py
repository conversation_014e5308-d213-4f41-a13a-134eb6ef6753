#!/usr/bin/env python3
"""
🔄 DATA SYNCHRONIZATION MANAGER
Zarządza synchronizacją danych między Python Mixer a GoBackend-Kratos
Zapewnia spójność danych i automatyczną synchronizację
"""

import asyncio
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Set
from dataclasses import dataclass, asdict
from pathlib import Path
import hashlib
from enum import Enum
import sqlite3
import aiosqlite

from .gobackend_client import EnhancedGoBackendClient, APIResponse
from .unified_bridge import TranscriptionData, EmailMetadata

logger = logging.getLogger(__name__)

class SyncStatus(Enum):
    """Status synchronizacji"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"

@dataclass
class SyncRecord:
    """Rekord synchronizacji"""
    id: str
    data_type: str  # transcription, email_metadata, customer, etc.
    source_id: str
    target_id: Optional[str]
    status: SyncStatus
    created_at: datetime
    updated_at: datetime
    attempts: int = 0
    last_error: Optional[str] = None
    data_hash: Optional[str] = None

@dataclass
class SyncStats:
    """Statystyki synchronizacji"""
    total_records: int = 0
    pending: int = 0
    completed: int = 0
    failed: int = 0
    skipped: int = 0
    last_sync: Optional[datetime] = None
    sync_rate: float = 0.0

class DataSynchronizationManager:
    """🔄 Manager synchronizacji danych"""
    
    def __init__(self, db_path: str = "sync_manager.db"):
        self.db_path = db_path
        self.gobackend_client = EnhancedGoBackendClient()
        
        # Konfiguracja synchronizacji
        self.sync_config = {
            "batch_size": 50,
            "max_retries": 3,
            "retry_delay": 60,  # sekundy
            "auto_sync_interval": 300,  # 5 minut
            "conflict_resolution": "source_wins"  # source_wins, target_wins, manual
        }
        
        # Kolejki synchronizacji
        self.sync_queues = {
            "transcriptions": asyncio.Queue(),
            "email_metadata": asyncio.Queue(),
            "customers": asyncio.Queue(),
            "equipment": asyncio.Queue(),
            "service_tickets": asyncio.Queue()
        }
        
        # Statystyki
        self.stats = SyncStats()
        
        # Flagi kontrolne
        self.auto_sync_enabled = True
        self.sync_in_progress = False
        
    async def initialize(self):
        """🚀 Inicjalizacja managera synchronizacji"""
        logger.info("🔄 Inicjalizacja Data Synchronization Manager...")
        
        # Utwórz bazę danych
        await self._create_database()
        
        # Załaduj statystyki
        await self._load_stats()
        
        # Uruchom auto-sync
        if self.auto_sync_enabled:
            asyncio.create_task(self._auto_sync_loop())
        
        # Uruchom procesory kolejek
        for data_type in self.sync_queues:
            asyncio.create_task(self._process_sync_queue(data_type))
        
        logger.info("✅ Data Synchronization Manager zainicjalizowany")
    
    async def _create_database(self):
        """🗄️ Utwórz bazę danych synchronizacji"""
        async with aiosqlite.connect(self.db_path) as db:
            await db.execute("""
                CREATE TABLE IF NOT EXISTS sync_records (
                    id TEXT PRIMARY KEY,
                    data_type TEXT NOT NULL,
                    source_id TEXT NOT NULL,
                    target_id TEXT,
                    status TEXT NOT NULL,
                    created_at TEXT NOT NULL,
                    updated_at TEXT NOT NULL,
                    attempts INTEGER DEFAULT 0,
                    last_error TEXT,
                    data_hash TEXT,
                    UNIQUE(data_type, source_id)
                )
            """)
            
            await db.execute("""
                CREATE TABLE IF NOT EXISTS sync_stats (
                    id INTEGER PRIMARY KEY,
                    total_records INTEGER DEFAULT 0,
                    pending INTEGER DEFAULT 0,
                    completed INTEGER DEFAULT 0,
                    failed INTEGER DEFAULT 0,
                    skipped INTEGER DEFAULT 0,
                    last_sync TEXT,
                    sync_rate REAL DEFAULT 0.0,
                    updated_at TEXT NOT NULL
                )
            """)
            
            await db.execute("""
                CREATE INDEX IF NOT EXISTS idx_sync_records_status 
                ON sync_records(status)
            """)
            
            await db.execute("""
                CREATE INDEX IF NOT EXISTS idx_sync_records_data_type 
                ON sync_records(data_type)
            """)
            
            await db.commit()
    
    async def _load_stats(self):
        """📊 Załaduj statystyki"""
        async with aiosqlite.connect(self.db_path) as db:
            async with db.execute("SELECT * FROM sync_stats ORDER BY id DESC LIMIT 1") as cursor:
                row = await cursor.fetchone()
                if row:
                    self.stats = SyncStats(
                        total_records=row[1],
                        pending=row[2],
                        completed=row[3],
                        failed=row[4],
                        skipped=row[5],
                        last_sync=datetime.fromisoformat(row[6]) if row[6] else None,
                        sync_rate=row[7]
                    )
    
    async def _save_stats(self):
        """💾 Zapisz statystyki"""
        async with aiosqlite.connect(self.db_path) as db:
            await db.execute("""
                INSERT INTO sync_stats 
                (total_records, pending, completed, failed, skipped, last_sync, sync_rate, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                self.stats.total_records,
                self.stats.pending,
                self.stats.completed,
                self.stats.failed,
                self.stats.skipped,
                self.stats.last_sync.isoformat() if self.stats.last_sync else None,
                self.stats.sync_rate,
                datetime.now().isoformat()
            ))
            await db.commit()
    
    def _calculate_data_hash(self, data: Dict[str, Any]) -> str:
        """🔐 Oblicz hash danych"""
        data_str = json.dumps(data, sort_keys=True, default=str)
        return hashlib.sha256(data_str.encode()).hexdigest()
    
    async def add_transcription_for_sync(self, transcription: TranscriptionData) -> str:
        """📝 Dodaj transkrypcję do synchronizacji"""
        sync_id = f"transcription_{transcription.email_id}_{datetime.now().timestamp()}"
        
        data_hash = self._calculate_data_hash(asdict(transcription))
        
        record = SyncRecord(
            id=sync_id,
            data_type="transcription",
            source_id=transcription.email_id,
            target_id=None,
            status=SyncStatus.PENDING,
            created_at=datetime.now(),
            updated_at=datetime.now(),
            data_hash=data_hash
        )
        
        await self._save_sync_record(record)
        await self.sync_queues["transcriptions"].put((record, transcription))
        
        logger.info(f"📝 Dodano transkrypcję {transcription.email_id} do synchronizacji")
        return sync_id
    
    async def add_email_metadata_for_sync(self, metadata: EmailMetadata) -> str:
        """📧 Dodaj metadane emaila do synchronizacji"""
        sync_id = f"email_{metadata.email_id}_{datetime.now().timestamp()}"
        
        data_hash = self._calculate_data_hash(asdict(metadata))
        
        record = SyncRecord(
            id=sync_id,
            data_type="email_metadata",
            source_id=metadata.email_id,
            target_id=None,
            status=SyncStatus.PENDING,
            created_at=datetime.now(),
            updated_at=datetime.now(),
            data_hash=data_hash
        )
        
        await self._save_sync_record(record)
        await self.sync_queues["email_metadata"].put((record, metadata))
        
        logger.info(f"📧 Dodano metadane emaila {metadata.email_id} do synchronizacji")
        return sync_id
    
    async def _save_sync_record(self, record: SyncRecord):
        """💾 Zapisz rekord synchronizacji"""
        async with aiosqlite.connect(self.db_path) as db:
            await db.execute("""
                INSERT OR REPLACE INTO sync_records 
                (id, data_type, source_id, target_id, status, created_at, updated_at, 
                 attempts, last_error, data_hash)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                record.id,
                record.data_type,
                record.source_id,
                record.target_id,
                record.status.value,
                record.created_at.isoformat(),
                record.updated_at.isoformat(),
                record.attempts,
                record.last_error,
                record.data_hash
            ))
            await db.commit()
    
    async def _update_sync_record_status(
        self, 
        record_id: str, 
        status: SyncStatus, 
        target_id: Optional[str] = None,
        error: Optional[str] = None
    ):
        """🔄 Aktualizuj status rekordu synchronizacji"""
        async with aiosqlite.connect(self.db_path) as db:
            await db.execute("""
                UPDATE sync_records 
                SET status = ?, updated_at = ?, target_id = ?, last_error = ?,
                    attempts = attempts + 1
                WHERE id = ?
            """, (
                status.value,
                datetime.now().isoformat(),
                target_id,
                error,
                record_id
            ))
            await db.commit()
    
    async def _process_sync_queue(self, data_type: str):
        """🔄 Przetwarzaj kolejkę synchronizacji"""
        queue = self.sync_queues[data_type]
        
        while True:
            try:
                record, data = await queue.get()
                
                # Sprawdź czy nie przekroczono maksymalnej liczby prób
                if record.attempts >= self.sync_config["max_retries"]:
                    await self._update_sync_record_status(
                        record.id, 
                        SyncStatus.FAILED,
                        error="Przekroczono maksymalną liczbę prób"
                    )
                    continue
                
                # Wykonaj synchronizację
                success = await self._sync_data(record, data)
                
                if success:
                    await self._update_sync_record_status(record.id, SyncStatus.COMPLETED)
                    self.stats.completed += 1
                else:
                    # Ponów próbę później
                    await asyncio.sleep(self.sync_config["retry_delay"])
                    await queue.put((record, data))
                
            except Exception as e:
                logger.error(f"❌ Błąd przetwarzania kolejki {data_type}: {e}")
                await asyncio.sleep(30)
    
    async def _sync_data(self, record: SyncRecord, data: Any) -> bool:
        """🔄 Synchronizuj dane"""
        try:
            async with self.gobackend_client as client:
                
                if record.data_type == "transcription":
                    response = await client.create_transcription(asdict(data))
                elif record.data_type == "email_metadata":
                    response = await client.create_email_metadata(asdict(data))
                else:
                    logger.warning(f"⚠️ Nieznany typ danych: {record.data_type}")
                    return False
                
                if response.success:
                    # Wyodrębnij ID z odpowiedzi
                    target_id = None
                    if response.data and "id" in response.data:
                        target_id = str(response.data["id"])
                    
                    await self._update_sync_record_status(
                        record.id, 
                        SyncStatus.COMPLETED,
                        target_id=target_id
                    )
                    
                    logger.info(f"✅ Zsynchronizowano {record.data_type} {record.source_id}")
                    return True
                else:
                    await self._update_sync_record_status(
                        record.id,
                        SyncStatus.FAILED,
                        error=response.error
                    )
                    
                    logger.error(f"❌ Błąd synchronizacji {record.data_type} {record.source_id}: {response.error}")
                    return False
        
        except Exception as e:
            await self._update_sync_record_status(
                record.id,
                SyncStatus.FAILED,
                error=str(e)
            )
            
            logger.error(f"❌ Wyjątek podczas synchronizacji {record.data_type} {record.source_id}: {e}")
            return False
    
    async def _auto_sync_loop(self):
        """🔄 Pętla automatycznej synchronizacji"""
        while self.auto_sync_enabled:
            try:
                await asyncio.sleep(self.sync_config["auto_sync_interval"])
                
                if not self.sync_in_progress:
                    await self.perform_incremental_sync()
                
            except Exception as e:
                logger.error(f"❌ Błąd w auto-sync loop: {e}")
                await asyncio.sleep(60)
    
    async def perform_full_sync(self) -> Dict[str, Any]:
        """🔄 Wykonaj pełną synchronizację"""
        if self.sync_in_progress:
            return {"error": "Synchronizacja już w toku"}
        
        self.sync_in_progress = True
        start_time = datetime.now()
        
        try:
            logger.info("🔄 Rozpoczynanie pełnej synchronizacji...")
            
            # Synchronizuj dane z dolores_email_archive
            archive_path = Path("dolores_email_archive")
            
            sync_results = {
                "transcriptions": {"processed": 0, "success": 0, "failed": 0},
                "email_metadata": {"processed": 0, "success": 0, "failed": 0}
            }
            
            if archive_path.exists():
                # Synchronizuj metadane emaili
                metadata_files = list((archive_path / "metadata").glob("*.json"))
                for metadata_file in metadata_files:
                    try:
                        with open(metadata_file, 'r') as f:
                            data = json.load(f)
                        
                        metadata = EmailMetadata(
                            email_id=data["email_id"],
                            subject=data["subject"],
                            sender=data["from"],
                            recipient=data["to"],
                            date=datetime.fromisoformat(data["processed_timestamp"]),
                            has_attachments=data["has_attachments"],
                            attachment_count=data["attachment_count"],
                            m4a_files=data["m4a_attachments"]
                        )
                        
                        await self.add_email_metadata_for_sync(metadata)
                        sync_results["email_metadata"]["processed"] += 1
                        
                    except Exception as e:
                        logger.error(f"❌ Błąd przetwarzania metadanych {metadata_file}: {e}")
                        sync_results["email_metadata"]["failed"] += 1
                
                # Synchronizuj transkrypcje
                transcription_files = list((archive_path / "transcriptions").glob("transcription_*.json"))
                for transcription_file in transcription_files:
                    try:
                        with open(transcription_file, 'r') as f:
                            data = json.load(f)
                        
                        transcription = TranscriptionData(
                            email_id=data["file_info"]["email_id"],
                            file_path=data["file_info"]["original_file"],
                            transcript=data["text"],
                            confidence=data["confidence"],
                            language=data["language"],
                            processing_time=data["processing_time"],
                            metadata=data["file_info"],
                            timestamp=datetime.now()
                        )
                        
                        await self.add_transcription_for_sync(transcription)
                        sync_results["transcriptions"]["processed"] += 1
                        
                    except Exception as e:
                        logger.error(f"❌ Błąd przetwarzania transkrypcji {transcription_file}: {e}")
                        sync_results["transcriptions"]["failed"] += 1
            
            # Aktualizuj statystyki
            await self._update_stats()
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            logger.info(f"✅ Pełna synchronizacja zakończona w {duration:.2f}s")
            
            return {
                "success": True,
                "duration": duration,
                "results": sync_results,
                "stats": asdict(self.stats)
            }
        
        finally:
            self.sync_in_progress = False
    
    async def perform_incremental_sync(self) -> Dict[str, Any]:
        """📈 Wykonaj synchronizację przyrostową"""
        logger.info("📈 Synchronizacja przyrostowa...")
        
        # Sprawdź rekordy oczekujące i nieudane
        await self._retry_failed_syncs()
        await self._update_stats()
        
        return {"success": True, "stats": asdict(self.stats)}
    
    async def _retry_failed_syncs(self):
        """🔄 Ponów nieudane synchronizacje"""
        async with aiosqlite.connect(self.db_path) as db:
            async with db.execute("""
                SELECT * FROM sync_records 
                WHERE status = ? AND attempts < ?
                ORDER BY created_at ASC
                LIMIT 100
            """, (SyncStatus.FAILED.value, self.sync_config["max_retries"])) as cursor:
                
                async for row in cursor:
                    record = SyncRecord(
                        id=row[0],
                        data_type=row[1],
                        source_id=row[2],
                        target_id=row[3],
                        status=SyncStatus(row[4]),
                        created_at=datetime.fromisoformat(row[5]),
                        updated_at=datetime.fromisoformat(row[6]),
                        attempts=row[7],
                        last_error=row[8],
                        data_hash=row[9]
                    )
                    
                    # Dodaj z powrotem do kolejki
                    if record.data_type in self.sync_queues:
                        # Tutaj trzeba by odtworzyć dane z pliku lub cache
                        # Na razie pomijamy
                        pass
    
    async def _update_stats(self):
        """📊 Aktualizuj statystyki"""
        async with aiosqlite.connect(self.db_path) as db:
            # Policz rekordy według statusu
            async with db.execute("""
                SELECT status, COUNT(*) FROM sync_records GROUP BY status
            """) as cursor:
                status_counts = {status.value: 0 for status in SyncStatus}
                async for row in cursor:
                    status_counts[row[0]] = row[1]
            
            self.stats.total_records = sum(status_counts.values())
            self.stats.pending = status_counts[SyncStatus.PENDING.value]
            self.stats.completed = status_counts[SyncStatus.COMPLETED.value]
            self.stats.failed = status_counts[SyncStatus.FAILED.value]
            self.stats.skipped = status_counts[SyncStatus.SKIPPED.value]
            self.stats.last_sync = datetime.now()
            
            if self.stats.total_records > 0:
                self.stats.sync_rate = (self.stats.completed / self.stats.total_records) * 100
        
        await self._save_stats()
    
    async def get_sync_status(self) -> Dict[str, Any]:
        """📊 Pobierz status synchronizacji"""
        await self._update_stats()
        
        return {
            "stats": asdict(self.stats),
            "config": self.sync_config,
            "auto_sync_enabled": self.auto_sync_enabled,
            "sync_in_progress": self.sync_in_progress,
            "queue_sizes": {
                name: queue.qsize() for name, queue in self.sync_queues.items()
            }
        }
    
    async def get_failed_syncs(self, limit: int = 50) -> List[Dict[str, Any]]:
        """❌ Pobierz nieudane synchronizacje"""
        failed_syncs = []
        
        async with aiosqlite.connect(self.db_path) as db:
            async with db.execute("""
                SELECT * FROM sync_records 
                WHERE status = ? 
                ORDER BY updated_at DESC 
                LIMIT ?
            """, (SyncStatus.FAILED.value, limit)) as cursor:
                
                async for row in cursor:
                    failed_syncs.append({
                        "id": row[0],
                        "data_type": row[1],
                        "source_id": row[2],
                        "target_id": row[3],
                        "status": row[4],
                        "created_at": row[5],
                        "updated_at": row[6],
                        "attempts": row[7],
                        "last_error": row[8]
                    })
        
        return failed_syncs

# Globalna instancja managera synchronizacji
sync_manager = DataSynchronizationManager()
