#!/usr/bin/env python3
"""
🎼 INTEGRATION ORCHESTRATOR
Główny orkiestrator integracji Python Mixer ↔ GoBackend-Kratos
Koordynuje wszystkie komponenty integracji i zapewnia spójność systemu
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
import json
from pathlib import Path

from .unified_bridge import UnifiedIntegrationBridge, TranscriptionData, EmailMetadata
from .gobackend_client import EnhancedGoBackendClient
from .sync_manager import DataSynchronizationManager
from .event_system import RealTimeEventSystem, EventType, EventPriority

logger = logging.getLogger(__name__)

@dataclass
class IntegrationHealth:
    """Stan zdrowia integracji"""
    overall_score: float
    components: Dict[str, Dict[str, Any]]
    last_check: datetime
    issues: List[str]
    recommendations: List[str]

class IntegrationOrchestrator:
    """🎼 Główny orkiestrator integracji"""
    
    def __init__(self):
        # Komponenty integracji
        self.bridge = UnifiedIntegrationBridge()
        self.gobackend_client = EnhancedGoBackendClient()
        self.sync_manager = DataSynchronizationManager()
        self.event_system = RealTimeEventSystem()
        
        # Status orkiestratora
        self.is_running = False
        self.start_time = None
        
        # Konfiguracja
        self.config = {
            "auto_sync_enabled": True,
            "real_time_events": True,
            "health_check_interval": 60,  # sekundy
            "email_processing_enabled": True,
            "transcription_enabled": True
        }
        
        # Metryki
        self.metrics = {
            "emails_processed": 0,
            "transcriptions_completed": 0,
            "sync_operations": 0,
            "events_emitted": 0,
            "errors": 0,
            "uptime": 0
        }
        
        # Kolejki zadań
        self.task_queue = asyncio.Queue()
        self.priority_tasks = asyncio.Queue()
        
        # Aktywne taski
        self.active_tasks = []
    
    async def initialize(self) -> bool:
        """🚀 Inicjalizacja orkiestratora"""
        logger.info("🎼 Inicjalizacja Integration Orchestrator...")
        
        try:
            # Inicjalizuj komponenty w odpowiedniej kolejności
            
            # 1. Event System (pierwszy - inne komponenty mogą emitować zdarzenia)
            await self.event_system.initialize()
            await self._emit_event(
                EventType.SERVICE_ONLINE,
                "orchestrator",
                {"component": "event_system"}
            )
            
            # 2. Sync Manager
            await self.sync_manager.initialize()
            await self._emit_event(
                EventType.SERVICE_ONLINE,
                "orchestrator",
                {"component": "sync_manager"}
            )
            
            # 3. Integration Bridge
            bridge_success = await self.bridge.initialize()
            if bridge_success:
                await self._emit_event(
                    EventType.SERVICE_ONLINE,
                    "orchestrator",
                    {"component": "integration_bridge"}
                )
            else:
                await self._emit_event(
                    EventType.SERVICE_DEGRADED,
                    "orchestrator",
                    {"component": "integration_bridge", "reason": "initialization_failed"}
                )
            
            # 4. Zarejestruj event handlers
            self._register_event_handlers()
            
            # 5. Uruchom background tasks
            await self._start_background_tasks()
            
            self.is_running = True
            self.start_time = datetime.now()
            
            logger.info("✅ Integration Orchestrator zainicjalizowany pomyślnie")
            
            await self._emit_event(
                EventType.SERVICE_ONLINE,
                "orchestrator",
                {"status": "fully_operational", "components": 4}
            )
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Błąd inicjalizacji Integration Orchestrator: {e}")
            
            await self._emit_event(
                EventType.SERVICE_OFFLINE,
                "orchestrator",
                {"error": str(e)}
            )
            
            return False
    
    def _register_event_handlers(self):
        """📝 Zarejestruj handlery zdarzeń"""
        
        # Handler dla zdarzeń emailowych
        async def email_event_handler(event):
            if event.type == EventType.EMAIL_RECEIVED:
                await self._handle_new_email(event.data)
            elif event.type == EventType.ATTACHMENT_EXTRACTED:
                await self._handle_new_attachment(event.data)
        
        # Handler dla zdarzeń transkrypcji
        async def transcription_event_handler(event):
            if event.type == EventType.TRANSCRIPTION_COMPLETED:
                await self._handle_transcription_completed(event.data)
                self.metrics["transcriptions_completed"] += 1
            elif event.type == EventType.TRANSCRIPTION_FAILED:
                await self._handle_transcription_failed(event.data)
                self.metrics["errors"] += 1
        
        # Handler dla zdarzeń synchronizacji
        async def sync_event_handler(event):
            if event.type == EventType.SYNC_COMPLETED:
                self.metrics["sync_operations"] += 1
            elif event.type == EventType.SYNC_FAILED:
                self.metrics["errors"] += 1
        
        # Zarejestruj handlery
        self.event_system.subscribe(
            [EventType.EMAIL_RECEIVED, EventType.ATTACHMENT_EXTRACTED],
            email_event_handler
        )
        
        self.event_system.subscribe(
            [EventType.TRANSCRIPTION_COMPLETED, EventType.TRANSCRIPTION_FAILED],
            transcription_event_handler
        )
        
        self.event_system.subscribe(
            [EventType.SYNC_COMPLETED, EventType.SYNC_FAILED],
            sync_event_handler
        )
    
    async def _start_background_tasks(self):
        """🔄 Uruchom zadania w tle"""
        
        # Task processor
        task = asyncio.create_task(self._task_processor())
        self.active_tasks.append(task)
        
        # Priority task processor
        task = asyncio.create_task(self._priority_task_processor())
        self.active_tasks.append(task)
        
        # Health monitor
        task = asyncio.create_task(self._health_monitor())
        self.active_tasks.append(task)
        
        # Metrics updater
        task = asyncio.create_task(self._metrics_updater())
        self.active_tasks.append(task)
        
        logger.info(f"🔄 Uruchomiono {len(self.active_tasks)} zadań w tle")
    
    async def _task_processor(self):
        """⚙️ Procesor zadań"""
        while self.is_running:
            try:
                task_data = await self.task_queue.get()
                await self._execute_task(task_data)
                
            except Exception as e:
                logger.error(f"❌ Błąd w task processor: {e}")
                await asyncio.sleep(5)
    
    async def _priority_task_processor(self):
        """⚡ Procesor zadań priorytetowych"""
        while self.is_running:
            try:
                task_data = await self.priority_tasks.get()
                await self._execute_task(task_data)
                
            except Exception as e:
                logger.error(f"❌ Błąd w priority task processor: {e}")
                await asyncio.sleep(1)
    
    async def _execute_task(self, task_data: Dict[str, Any]):
        """🔧 Wykonaj zadanie"""
        task_type = task_data.get("type")
        
        try:
            if task_type == "process_email":
                await self._process_email_task(task_data)
            elif task_type == "sync_transcription":
                await self._sync_transcription_task(task_data)
            elif task_type == "sync_email_metadata":
                await self._sync_email_metadata_task(task_data)
            elif task_type == "health_check":
                await self._health_check_task(task_data)
            else:
                logger.warning(f"⚠️ Nieznany typ zadania: {task_type}")
        
        except Exception as e:
            logger.error(f"❌ Błąd wykonania zadania {task_type}: {e}")
            self.metrics["errors"] += 1
    
    async def _health_monitor(self):
        """🏥 Monitor zdrowia systemu"""
        while self.is_running:
            try:
                await asyncio.sleep(self.config["health_check_interval"])
                
                health = await self.get_integration_health()
                
                if health.overall_score < 70:
                    await self._emit_event(
                        EventType.SERVICE_DEGRADED,
                        "orchestrator",
                        {
                            "health_score": health.overall_score,
                            "issues": health.issues
                        },
                        priority=EventPriority.HIGH
                    )
                
                # Zapisz raport zdrowia
                await self._save_health_report(health)
                
            except Exception as e:
                logger.error(f"❌ Błąd w health monitor: {e}")
                await asyncio.sleep(60)
    
    async def _metrics_updater(self):
        """📊 Aktualizator metryk"""
        while self.is_running:
            try:
                await asyncio.sleep(30)  # Co 30 sekund
                
                # Aktualizuj uptime
                if self.start_time:
                    self.metrics["uptime"] = (datetime.now() - self.start_time).total_seconds()
                
                # Zapisz metryki
                await self._save_metrics()
                
            except Exception as e:
                logger.error(f"❌ Błąd w metrics updater: {e}")
                await asyncio.sleep(60)
    
    async def _handle_new_email(self, email_data: Dict[str, Any]):
        """📧 Obsłuż nowy email"""
        logger.info(f"📧 Przetwarzanie nowego emaila: {email_data.get('email_id')}")
        
        # Dodaj do kolejki zadań
        await self.task_queue.put({
            "type": "process_email",
            "data": email_data,
            "timestamp": datetime.now().isoformat()
        })
        
        self.metrics["emails_processed"] += 1
    
    async def _handle_new_attachment(self, attachment_data: Dict[str, Any]):
        """📎 Obsłuż nowy załącznik"""
        if attachment_data.get("file_type") == "m4a":
            logger.info(f"🎵 Nowy plik M4A do transkrypcji: {attachment_data.get('filename')}")
            
            # Dodaj do kolejki priorytetowej (transkrypcja ma wysoki priorytet)
            await self.priority_tasks.put({
                "type": "transcribe_audio",
                "data": attachment_data,
                "timestamp": datetime.now().isoformat()
            })
    
    async def _handle_transcription_completed(self, transcription_data: Dict[str, Any]):
        """✅ Obsłuż ukończoną transkrypcję"""
        logger.info(f"✅ Transkrypcja ukończona: {transcription_data.get('email_id')}")
        
        # Utwórz obiekt TranscriptionData
        transcription = TranscriptionData(
            email_id=transcription_data["email_id"],
            file_path=transcription_data["file_path"],
            transcript=transcription_data["transcript"],
            confidence=transcription_data.get("confidence", 0.0),
            language=transcription_data.get("language", "pl"),
            processing_time=transcription_data.get("processing_time", 0.0),
            metadata=transcription_data.get("metadata", {}),
            timestamp=datetime.now()
        )
        
        # Dodaj do synchronizacji
        await self.sync_manager.add_transcription_for_sync(transcription)
        
        # Wyślij bezpośrednio do GoBackend
        await self.bridge.send_transcription_to_gobackend(transcription)
    
    async def _handle_transcription_failed(self, error_data: Dict[str, Any]):
        """❌ Obsłuż błąd transkrypcji"""
        logger.error(f"❌ Błąd transkrypcji: {error_data}")
        
        # Możesz dodać logikę retry lub alertów
    
    async def _emit_event(
        self, 
        event_type: EventType, 
        source: str, 
        data: Dict[str, Any],
        priority: EventPriority = EventPriority.NORMAL
    ):
        """⚡ Wyemituj zdarzenie"""
        await self.event_system.emit(
            event_type=event_type,
            source=source,
            data=data,
            priority=priority
        )
        
        self.metrics["events_emitted"] += 1
    
    async def process_dolores_email_archive(self):
        """📧 Przetwórz archiwum emaili dolores"""
        logger.info("📧 Rozpoczynanie przetwarzania archiwum emaili dolores...")
        
        archive_path = Path("dolores_email_archive")
        
        if not archive_path.exists():
            logger.warning("⚠️ Archiwum emaili dolores nie istnieje")
            return
        
        # Przetwórz metadane emaili
        metadata_path = archive_path / "metadata"
        if metadata_path.exists():
            metadata_files = list(metadata_path.glob("*.json"))
            
            for metadata_file in metadata_files:
                try:
                    with open(metadata_file, 'r') as f:
                        data = json.load(f)
                    
                    # Wyemituj zdarzenie nowego emaila
                    await self._emit_event(
                        EventType.EMAIL_PROCESSED,
                        "dolores_archive",
                        data
                    )
                    
                except Exception as e:
                    logger.error(f"❌ Błąd przetwarzania metadanych {metadata_file}: {e}")
        
        # Przetwórz transkrypcje
        transcriptions_path = archive_path / "transcriptions"
        if transcriptions_path.exists():
            transcription_files = list(transcriptions_path.glob("transcription_*.json"))
            
            for transcription_file in transcription_files:
                try:
                    with open(transcription_file, 'r') as f:
                        data = json.load(f)
                    
                    # Wyemituj zdarzenie ukończonej transkrypcji
                    await self._emit_event(
                        EventType.TRANSCRIPTION_COMPLETED,
                        "dolores_archive",
                        data
                    )
                    
                except Exception as e:
                    logger.error(f"❌ Błąd przetwarzania transkrypcji {transcription_file}: {e}")
        
        logger.info("✅ Przetwarzanie archiwum emaili dolores zakończone")
    
    async def get_integration_health(self) -> IntegrationHealth:
        """🏥 Pobierz stan zdrowia integracji"""
        components = {}
        issues = []
        recommendations = []
        
        # Sprawdź Integration Bridge
        bridge_health = self.bridge.get_integration_health()
        components["integration_bridge"] = bridge_health
        
        if bridge_health["health_score"] < 80:
            issues.append(f"Integration Bridge: {bridge_health['health_score']:.1f}% zdrowia")
        
        # Sprawdź Sync Manager
        sync_status = await self.sync_manager.get_sync_status()
        components["sync_manager"] = sync_status
        
        if sync_status["stats"]["sync_rate"] < 90:
            issues.append(f"Sync Manager: {sync_status['stats']['sync_rate']:.1f}% sukcesu synchronizacji")
        
        # Sprawdź Event System
        event_stats = self.event_system.event_bus.get_stats()
        components["event_system"] = event_stats
        
        # Sprawdź GoBackend Client
        async with self.gobackend_client as client:
            client_metrics = client.get_client_metrics()
            components["gobackend_client"] = client_metrics
            
            if client_metrics["success_rate"] < 95:
                issues.append(f"GoBackend Client: {client_metrics['success_rate']:.1f}% sukcesu żądań")
        
        # Oblicz ogólny wynik zdrowia
        health_scores = []
        
        if "health_score" in bridge_health:
            health_scores.append(bridge_health["health_score"])
        
        if sync_status["stats"]["sync_rate"] > 0:
            health_scores.append(sync_status["stats"]["sync_rate"])
        
        if client_metrics["success_rate"] > 0:
            health_scores.append(client_metrics["success_rate"])
        
        overall_score = sum(health_scores) / len(health_scores) if health_scores else 0
        
        # Generuj rekomendacje
        if overall_score < 70:
            recommendations.append("Sprawdź połączenia sieciowe z GoBackend")
            recommendations.append("Zrestartuj komponenty z niskim wynikiem zdrowia")
        
        if len(issues) > 3:
            recommendations.append("Rozważ zwiększenie zasobów systemowych")
        
        return IntegrationHealth(
            overall_score=overall_score,
            components=components,
            last_check=datetime.now(),
            issues=issues,
            recommendations=recommendations
        )
    
    async def _save_health_report(self, health: IntegrationHealth):
        """💾 Zapisz raport zdrowia"""
        report_path = Path("integration_health.json")
        
        with open(report_path, 'w') as f:
            json.dump(asdict(health), f, indent=2, default=str)
    
    async def _save_metrics(self):
        """💾 Zapisz metryki"""
        metrics_path = Path("integration_metrics.json")
        
        metrics_data = {
            **self.metrics,
            "timestamp": datetime.now().isoformat(),
            "config": self.config
        }
        
        with open(metrics_path, 'w') as f:
            json.dump(metrics_data, f, indent=2)
    
    async def get_status(self) -> Dict[str, Any]:
        """📊 Pobierz status orkiestratora"""
        health = await self.get_integration_health()
        
        return {
            "is_running": self.is_running,
            "start_time": self.start_time.isoformat() if self.start_time else None,
            "uptime": self.metrics["uptime"],
            "metrics": self.metrics,
            "config": self.config,
            "health": asdict(health),
            "active_tasks": len(self.active_tasks),
            "queue_sizes": {
                "task_queue": self.task_queue.qsize(),
                "priority_tasks": self.priority_tasks.qsize()
            }
        }
    
    async def shutdown(self):
        """🛑 Zamknij orkiestrator"""
        logger.info("🛑 Zamykanie Integration Orchestrator...")
        
        self.is_running = False
        
        # Anuluj aktywne taski
        for task in self.active_tasks:
            task.cancel()
        
        await asyncio.gather(*self.active_tasks, return_exceptions=True)
        
        # Zamknij komponenty
        await self.event_system.shutdown()
        
        await self._emit_event(
            EventType.SERVICE_OFFLINE,
            "orchestrator",
            {"reason": "shutdown"}
        )
        
        logger.info("✅ Integration Orchestrator zamknięty")

# Globalna instancja orkiestratora
integration_orchestrator = IntegrationOrchestrator()
