#!/usr/bin/env python3
"""
🔗 ENHANCED GOBACKEND CLIENT
Zaawansowany klient do komunikacji z GoBackend-Kratos
Zapewnia niezawodną komunikację z retry logic i circuit breaker
"""

import asyncio
import aiohttp
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, asdict
from enum import Enum
import backoff
from pathlib import Path

logger = logging.getLogger(__name__)

class CircuitBreakerState(Enum):
    """Stan Circuit Breaker"""
    CLOSED = "closed"      # Normalny stan
    OPEN = "open"          # Błędy - blokuj żądania
    HALF_OPEN = "half_open"  # Test czy usługa wróciła

@dataclass
class APIResponse:
    """Odpowiedź z API"""
    success: bool
    status_code: int
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    timestamp: datetime = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()

class CircuitBreaker:
    """🔌 Circuit Breaker dla API calls"""
    
    def __init__(self, failure_threshold: int = 5, timeout: int = 60):
        self.failure_threshold = failure_threshold
        self.timeout = timeout
        self.failure_count = 0
        self.last_failure_time = None
        self.state = CircuitBreakerState.CLOSED
    
    def can_execute(self) -> bool:
        """Sprawdź czy można wykonać żądanie"""
        if self.state == CircuitBreakerState.CLOSED:
            return True
        elif self.state == CircuitBreakerState.OPEN:
            if datetime.now() - self.last_failure_time > timedelta(seconds=self.timeout):
                self.state = CircuitBreakerState.HALF_OPEN
                return True
            return False
        else:  # HALF_OPEN
            return True
    
    def record_success(self):
        """Zapisz sukces"""
        self.failure_count = 0
        self.state = CircuitBreakerState.CLOSED
    
    def record_failure(self):
        """Zapisz błąd"""
        self.failure_count += 1
        self.last_failure_time = datetime.now()
        
        if self.failure_count >= self.failure_threshold:
            self.state = CircuitBreakerState.OPEN

class EnhancedGoBackendClient:
    """🚀 Zaawansowany klient GoBackend"""
    
    def __init__(self, base_url: str = "http://localhost", timeout: int = 30):
        self.base_url = base_url
        self.timeout = timeout
        
        # Endpointy usług
        self.endpoints = {
            "crm": {"port": 8080, "base_path": "/api/v1"},
            "mcp": {"port": 8081, "base_path": ""},
            "email_intelligence": {"port": 8082, "base_path": "/api"}
        }
        
        # Circuit Breakers dla każdej usługi
        self.circuit_breakers = {
            service: CircuitBreaker() for service in self.endpoints.keys()
        }
        
        # Metryki
        self.metrics = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "circuit_breaker_trips": 0,
            "last_request": None
        }
        
        # Session pool
        self.session = None
    
    async def __aenter__(self):
        """Async context manager entry"""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=self.timeout),
            connector=aiohttp.TCPConnector(limit=100, limit_per_host=30)
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()
    
    def _get_service_url(self, service: str, path: str = "") -> str:
        """Pobierz URL usługi"""
        if service not in self.endpoints:
            raise ValueError(f"Nieznana usługa: {service}")
        
        endpoint = self.endpoints[service]
        return f"{self.base_url}:{endpoint['port']}{endpoint['base_path']}{path}"
    
    @backoff.on_exception(
        backoff.expo,
        (aiohttp.ClientError, asyncio.TimeoutError),
        max_tries=3,
        max_time=60
    )
    async def _make_request(
        self, 
        service: str, 
        method: str, 
        path: str, 
        data: Optional[Dict] = None,
        params: Optional[Dict] = None
    ) -> APIResponse:
        """🔄 Wykonaj żądanie HTTP z retry logic"""
        
        # Sprawdź Circuit Breaker
        circuit_breaker = self.circuit_breakers[service]
        if not circuit_breaker.can_execute():
            self.metrics["circuit_breaker_trips"] += 1
            return APIResponse(
                success=False,
                status_code=503,
                error=f"Circuit breaker OPEN dla usługi {service}"
            )
        
        self.metrics["total_requests"] += 1
        self.metrics["last_request"] = datetime.now()
        
        try:
            url = self._get_service_url(service, path)
            
            if not self.session:
                self.session = aiohttp.ClientSession(
                    timeout=aiohttp.ClientTimeout(total=self.timeout)
                )
            
            async with self.session.request(
                method=method,
                url=url,
                json=data,
                params=params
            ) as response:
                
                response_data = None
                if response.content_type == 'application/json':
                    response_data = await response.json()
                else:
                    response_text = await response.text()
                    if response_text:
                        try:
                            response_data = json.loads(response_text)
                        except json.JSONDecodeError:
                            response_data = {"message": response_text}
                
                if response.status < 400:
                    circuit_breaker.record_success()
                    self.metrics["successful_requests"] += 1
                    
                    return APIResponse(
                        success=True,
                        status_code=response.status,
                        data=response_data
                    )
                else:
                    circuit_breaker.record_failure()
                    self.metrics["failed_requests"] += 1
                    
                    return APIResponse(
                        success=False,
                        status_code=response.status,
                        data=response_data,
                        error=f"HTTP {response.status}: {response_data}"
                    )
        
        except Exception as e:
            circuit_breaker.record_failure()
            self.metrics["failed_requests"] += 1
            
            logger.error(f"❌ Błąd żądania do {service}{path}: {e}")
            
            return APIResponse(
                success=False,
                status_code=0,
                error=str(e)
            )
    
    # === CRM API METHODS ===
    
    async def create_transcription(self, transcription_data: Dict[str, Any]) -> APIResponse:
        """📝 Utwórz nową transkrypcję w CRM"""
        return await self._make_request(
            service="crm",
            method="POST",
            path="/transcriptions",
            data=transcription_data
        )
    
    async def create_email_metadata(self, email_data: Dict[str, Any]) -> APIResponse:
        """📧 Utwórz metadane emaila w CRM"""
        return await self._make_request(
            service="crm",
            method="POST",
            path="/emails/metadata",
            data=email_data
        )
    
    async def get_customer_by_phone(self, phone: str) -> APIResponse:
        """👤 Pobierz klienta po numerze telefonu"""
        return await self._make_request(
            service="crm",
            method="GET",
            path="/customers/by-phone",
            params={"phone": phone}
        )
    
    async def create_customer(self, customer_data: Dict[str, Any]) -> APIResponse:
        """👤 Utwórz nowego klienta"""
        return await self._make_request(
            service="crm",
            method="POST",
            path="/customers",
            data=customer_data
        )
    
    async def create_service_ticket(self, ticket_data: Dict[str, Any]) -> APIResponse:
        """🎫 Utwórz zgłoszenie serwisowe"""
        return await self._make_request(
            service="crm",
            method="POST",
            path="/service-tickets",
            data=ticket_data
        )
    
    async def get_equipment_by_serial(self, serial: str) -> APIResponse:
        """🔧 Pobierz urządzenie po numerze seryjnym"""
        return await self._make_request(
            service="crm",
            method="GET",
            path="/equipment/by-serial",
            params={"serial": serial}
        )
    
    async def create_equipment(self, equipment_data: Dict[str, Any]) -> APIResponse:
        """🔧 Utwórz nowe urządzenie"""
        return await self._make_request(
            service="crm",
            method="POST",
            path="/equipment",
            data=equipment_data
        )
    
    # === MCP API METHODS ===
    
    async def mcp_list_tools(self) -> APIResponse:
        """🛠️ Pobierz listę narzędzi MCP"""
        return await self._make_request(
            service="mcp",
            method="POST",
            path="/mcp",
            data={
                "jsonrpc": "2.0",
                "id": 1,
                "method": "tools/list",
                "params": {}
            }
        )
    
    async def mcp_call_tool(self, tool_name: str, arguments: Dict[str, Any]) -> APIResponse:
        """🔧 Wywołaj narzędzie MCP"""
        return await self._make_request(
            service="mcp",
            method="POST",
            path="/mcp",
            data={
                "jsonrpc": "2.0",
                "id": 1,
                "method": "tools/call",
                "params": {
                    "name": tool_name,
                    "arguments": arguments
                }
            }
        )
    
    # === EMAIL INTELLIGENCE API METHODS ===
    
    async def analyze_email_content(self, email_content: str) -> APIResponse:
        """🧠 Analizuj treść emaila"""
        return await self._make_request(
            service="email_intelligence",
            method="POST",
            path="/analyze",
            data={"content": email_content}
        )
    
    async def extract_entities(self, text: str) -> APIResponse:
        """🏷️ Wyodrębnij encje z tekstu"""
        return await self._make_request(
            service="email_intelligence",
            method="POST",
            path="/entities",
            data={"text": text}
        )
    
    async def classify_intent(self, text: str) -> APIResponse:
        """🎯 Klasyfikuj intencję tekstu"""
        return await self._make_request(
            service="email_intelligence",
            method="POST",
            path="/intent",
            data={"text": text}
        )
    
    # === HEALTH & MONITORING ===
    
    async def check_service_health(self, service: str) -> APIResponse:
        """🏥 Sprawdź zdrowie usługi"""
        health_paths = {
            "crm": "/health",
            "mcp": "/health",
            "email_intelligence": "/health"
        }
        
        path = health_paths.get(service, "/health")
        return await self._make_request(
            service=service,
            method="GET",
            path=path
        )
    
    async def get_service_metrics(self, service: str) -> APIResponse:
        """📊 Pobierz metryki usługi"""
        return await self._make_request(
            service=service,
            method="GET",
            path="/metrics"
        )
    
    def get_client_metrics(self) -> Dict[str, Any]:
        """📈 Pobierz metryki klienta"""
        return {
            **self.metrics,
            "circuit_breakers": {
                service: {
                    "state": cb.state.value,
                    "failure_count": cb.failure_count,
                    "last_failure": cb.last_failure_time.isoformat() if cb.last_failure_time else None
                }
                for service, cb in self.circuit_breakers.items()
            },
            "success_rate": (
                self.metrics["successful_requests"] / max(self.metrics["total_requests"], 1)
            ) * 100
        }
    
    async def bulk_sync_transcriptions(self, transcriptions: List[Dict[str, Any]]) -> List[APIResponse]:
        """📦 Masowa synchronizacja transkrypcji"""
        results = []
        
        # Przetwarzaj w batch'ach po 10
        batch_size = 10
        for i in range(0, len(transcriptions), batch_size):
            batch = transcriptions[i:i + batch_size]
            
            # Równoległe przetwarzanie batch'a
            tasks = [
                self.create_transcription(transcription)
                for transcription in batch
            ]
            
            batch_results = await asyncio.gather(*tasks, return_exceptions=True)
            results.extend(batch_results)
            
            # Krótka przerwa między batch'ami
            await asyncio.sleep(0.1)
        
        return results
    
    async def bulk_sync_emails(self, emails: List[Dict[str, Any]]) -> List[APIResponse]:
        """📧 Masowa synchronizacja emaili"""
        results = []
        
        batch_size = 20
        for i in range(0, len(emails), batch_size):
            batch = emails[i:i + batch_size]
            
            tasks = [
                self.create_email_metadata(email)
                for email in batch
            ]
            
            batch_results = await asyncio.gather(*tasks, return_exceptions=True)
            results.extend(batch_results)
            
            await asyncio.sleep(0.1)
        
        return results

# Globalna instancja klienta
gobackend_client = EnhancedGoBackendClient()
