#!/usr/bin/env python3
"""
🌉 INTEGRATION PACKAGE
Pakiet integracji Python Mixer ↔ GoBackend-Kratos
"""

from .unified_bridge import UnifiedIntegrationBridge, TranscriptionData, EmailMetadata
from .gobackend_client import EnhancedGoBackendClient
from .sync_manager import DataSynchronizationManager
from .event_system import RealTimeEventSystem, EventType, EventPriority
from .orchestrator import IntegrationOrchestrator

__all__ = [
    'UnifiedIntegrationBridge',
    'TranscriptionData', 
    'EmailMetadata',
    'EnhancedGoBackendClient',
    'DataSynchronizationManager',
    'RealTimeEventSystem',
    'EventType',
    'EventPriority',
    'IntegrationOrchestrator'
]
